package ${bussiPackage}.${entityPackage}.controller;

import org.jeecg.common.system.query.QueryGenerator;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.jeecg.common.system.query.QueryRuleEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.api.vo.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.ModelAndView;
import java.util.Arrays;
import java.util.HashMap;
import org.jeecg.common.util.oConvertUtils;
<#list subTables as sub>
import ${bussiPackage}.${entityPackage}.entity.${sub.entityName};
</#list>
import ${bussiPackage}.${entityPackage}.entity.${entityName};
import ${bussiPackage}.${entityPackage}.service.I${entityName}Service;
<#list subTables as sub>
import ${bussiPackage}.${entityPackage}.service.I${sub.entityName}Service;
</#list>
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.shiro.authz.annotation.RequiresPermissions;
<#assign has_multi_query_field=false>
<#list originalColumns as po>
<#if po.isQuery=='Y' && (po.classType=='list' || po.classType=='list_multi' || po.classType=='radio' || po.classType=='checkbox')>
   <#assign has_multi_query_field=true>
</#if>
</#list>
<#assign enhanceJavaList=[]>
<#if tableVo.extendParams?? && tableVo.extendParams.enhanceJavaList??>
  <#assign enhanceJavaList = tableVo.extendParams.enhanceJavaList?filter(enhance -> enhance??)>
</#if>
 /**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Tag(name="${tableVo.ftlDescription}")
@RestController
@RequestMapping("/${entityPackagePath}/${entityName?uncap_first}")
@Slf4j
public class ${entityName}Controller extends JeecgController<${entityName}, I${entityName}Service> {

	@Autowired
	private I${entityName}Service ${entityName?uncap_first}Service;
	<#list subTables as sub>

	@Autowired
	private I${sub.entityName}Service ${sub.entityName?uncap_first}Service;
	</#list>


	/*---------------------------------主表处理-begin-------------------------------------*/

	/**
	 * 分页列表查询
	 * @param ${entityName?uncap_first}
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "${tableVo.ftlDescription}-分页列表查询")
	@Operation(summary="${tableVo.ftlDescription}-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<${entityName}>> queryPageList(${entityName} ${entityName?uncap_first},
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
    <#if enhanceJavaList?size gt 0>
     <#list enhanceJavaList as enhanceJava>
     <#if enhanceJava.buttonCode=='query' && enhanceJava.event=='start' && enhanceJava.activeStatus=='1'>
           //TODO 查询前触发的方法，代码生成后，请手工实现增强类逻辑;
           //${entityName?uncap_first}Service.beforeQuery()
     </#if>
     </#list>
    </#if>
    <#if has_multi_query_field>
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
    <#list originalColumns as po>
        <#if po.isQuery=='Y' && (po.classType=='list' || po.classType=='list_multi' || po.classType=='radio' || po.classType=='checkbox')>
        customeRuleMap.put("${po.fieldName}", QueryRuleEnum.LIKE_WITH_OR);
        </#if>
    </#list>
        QueryWrapper<${entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${entityName?uncap_first}, req.getParameterMap(),customeRuleMap);
    <#else>
      	QueryWrapper<${entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${entityName?uncap_first}, req.getParameterMap());
    </#if>
		Page<${entityName}> page = new Page<${entityName}>(pageNo, pageSize);
		IPage<${entityName}> pageList = ${entityName?uncap_first}Service.page(page, queryWrapper);
<#if enhanceJavaList?size gt 0>
 <#list enhanceJavaList as enhanceJava>
 <#if enhanceJava.buttonCode=='query' && enhanceJava.event=='end' && enhanceJava.activeStatus=='1'>
      //TODO 查询后触发的方法，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.afterQuery()
 </#if>
 </#list>
</#if>
		return Result.OK(pageList);
	}

	/**
     *   添加
     * @param ${entityName?uncap_first}
     * @return
     */
    @AutoLog(value = "${tableVo.ftlDescription}-添加")
    @Operation(summary="${tableVo.ftlDescription}-添加")
    @RequiresPermissions("${entityPackage}:${tableName}:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody ${entityName} ${entityName?uncap_first}) {
     <#if enhanceJavaList?size gt 0>
       <#list enhanceJavaList as enhanceJava>
       <#if enhanceJava.buttonCode=='add' && enhanceJava.event=='start'  && enhanceJava.activeStatus=='1'>
           //TODO 新增前的处理方法，代码生成后，请手工实现增强类逻辑;
           //${entityName?uncap_first}Service.beforeAdd()
       </#if>
       </#list>
     </#if>
        ${entityName?uncap_first}Service.save(${entityName?uncap_first});
<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='add' && enhanceJava.event=='end'  && enhanceJava.activeStatus=='1'>
       //TODO 新增后的处理方法，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.afterAdd()
   </#if>
  </#list>
</#if>
        return Result.OK("添加成功！");
    }

    /**
     *  编辑
     * @param ${entityName?uncap_first}
     * @return
     */
    @AutoLog(value = "${tableVo.ftlDescription}-编辑")
    @Operation(summary="${tableVo.ftlDescription}-编辑")
    @RequiresPermissions("${entityPackage}:${tableName}:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
    public Result<String> edit(@RequestBody ${entityName} ${entityName?uncap_first}) {
<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='edit' && enhanceJava.event=='start'  && enhanceJava.activeStatus=='1'>
       //TODO 编辑前，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.beforeEdit()
  </#if>
  </#list>
</#if>
        ${entityName?uncap_first}Service.updateById(${entityName?uncap_first});
<#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='edit' && enhanceJava.event=='end' && enhanceJava.activeStatus=='1'>
      //TODO 编辑后，代码生成后，请手工实现增强类逻辑;
      //${entityName?uncap_first}Service.afterEdit()
  </#if>
  </#list>
</#if>
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     * @param id
     * @return
     */
    @AutoLog(value = "${tableVo.ftlDescription}-通过id删除")
    @Operation(summary="${tableVo.ftlDescription}-通过id删除")
    @RequiresPermissions("${entityPackage}:${tableName}:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name="id",required=true) String id) {
        ${entityName?uncap_first}Service.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     * @param ids
     * @return
     */
    @AutoLog(value = "${tableVo.ftlDescription}-批量删除")
    @Operation(summary="${tableVo.ftlDescription}-批量删除")
    @RequiresPermissions("${entityPackage}:${tableName}:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        this.${entityName?uncap_first}Service.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 导出
     * @return
     */
    @RequiresPermissions("${entityPackage}:${tableName}:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ${entityName} ${entityName?uncap_first}) {
 <#if enhanceJavaList?size gt 0>
  <#list enhanceJavaList as enhanceJava>
  <#if enhanceJava.buttonCode=='export' && enhanceJava.event=='start' && enhanceJava.activeStatus=='1'>
       //TODO 导出前，代码生成后，请手工实现增强类逻辑;
       //${entityName?uncap_first}Service.beforeExport()
  </#if>
  </#list>
 </#if>
        return super.exportXls(request, ${entityName?uncap_first}, ${entityName}.class, "${tableVo.ftlDescription}");
    }

    /**
     * 导入
     * @return
     */
    @RequiresPermissions("${entityPackage}:${tableName}:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
       <#if enhanceJavaList?size gt 0>
        <#list enhanceJavaList as enhanceJava>
        <#if enhanceJava.buttonCode=='import' && enhanceJava.event=='start' && enhanceJava.activeStatus=='1'>
             //TODO 导入前，代码生成后，请手工实现增强类逻辑;
             //${entityName?uncap_first}Service.beforeImport()
        </#if>
        </#list>
       </#if>
        return super.importExcel(request, response, ${entityName}.class);
    }
	/*---------------------------------主表处理-end-------------------------------------*/
	
	<#list subTables as sub>

    /*--------------------------------子表处理-${sub.ftlDescription}-begin----------------------------------------------*/
	/**
	 * 通过主表ID查询
	 * @return
	 */
	//@AutoLog(value = "${sub.ftlDescription}-通过主表ID查询")
	@Operation(summary="${sub.ftlDescription}-通过主表ID查询")
	@GetMapping(value = "/list${sub.entityName}ByMainId")
    public Result<IPage<${sub.entityName}>> list${sub.entityName}ByMainId(${sub.entityName} ${sub.entityName?uncap_first},
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        QueryWrapper<${sub.entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${sub.entityName?uncap_first}, req.getParameterMap());
        Page<${sub.entityName}> page = new Page<${sub.entityName}>(pageNo, pageSize);
        IPage<${sub.entityName}> pageList = ${sub.entityName?uncap_first}Service.page(page, queryWrapper);
        return Result.OK(pageList);
    }

	/**
	 * 添加
	 * @param ${sub.entityName?uncap_first}
	 * @return
	 */
	@AutoLog(value = "${sub.ftlDescription}-添加")
	@Operation(summary="${sub.ftlDescription}-添加")
	@PostMapping(value = "/add${sub.entityName}")
	public Result<String> add${sub.entityName}(@RequestBody ${sub.entityName} ${sub.entityName?uncap_first}) {
		${sub.entityName?uncap_first}Service.save(${sub.entityName?uncap_first});
		return Result.OK("添加成功！");
	}

    /**
	 * 编辑
	 * @param ${sub.entityName?uncap_first}
	 * @return
	 */
	@AutoLog(value = "${sub.ftlDescription}-编辑")
	@Operation(summary="${sub.ftlDescription}-编辑")
	@RequestMapping(value = "/edit${sub.entityName}", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit${sub.entityName}(@RequestBody ${sub.entityName} ${sub.entityName?uncap_first}) {
		${sub.entityName?uncap_first}Service.updateById(${sub.entityName?uncap_first});
		return Result.OK("编辑成功!");
	}

	/**
	 * 通过id删除
	 * @param id
	 * @return
	 */
	@AutoLog(value = "${sub.ftlDescription}-通过id删除")
	@Operation(summary="${sub.ftlDescription}-通过id删除")
	@DeleteMapping(value = "/delete${sub.entityName}")
	public Result<String> delete${sub.entityName}(@RequestParam(name="id",required=true) String id) {
		${sub.entityName?uncap_first}Service.removeById(id);
		return Result.OK("删除成功!");
	}

	/**
	 * 批量删除
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "${sub.ftlDescription}-批量删除")
	@Operation(summary="${sub.ftlDescription}-批量删除")
	@DeleteMapping(value = "/deleteBatch${sub.entityName}")
	public Result<String> deleteBatch${sub.entityName}(@RequestParam(name="ids",required=true) String ids) {
	    this.${sub.entityName?uncap_first}Service.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功!");
	}

    /**
     * 导出
     * @return
     */
    @RequestMapping(value = "/export${sub.entityName}")
    public ModelAndView export${sub.entityName}(HttpServletRequest request, ${sub.entityName} ${sub.entityName?uncap_first}) {
		 // Step.1 组装查询条件
		 QueryWrapper<${sub.entityName}> queryWrapper = QueryGenerator.initQueryWrapper(${sub.entityName?uncap_first}, request.getParameterMap());
		 LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		 // Step.2 获取导出数据
		 List<${sub.entityName}> pageList = ${sub.entityName?uncap_first}Service.list(queryWrapper);
		 List<${sub.entityName}> exportList = null;

		 // 过滤选中数据
		 String selections = request.getParameter("selections");
		 if (oConvertUtils.isNotEmpty(selections)) {
			 List<String> selectionList = Arrays.asList(selections.split(","));
			 exportList = pageList.stream().filter(item -> selectionList.contains(item.getId())).collect(Collectors.toList());
		 } else {
			 exportList = pageList;
		 }

		 // Step.3 AutoPoi 导出Excel
		 ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
		 //此处设置的filename无效,前端会重更新设置一下
		 mv.addObject(NormalExcelConstants.FILE_NAME, "${sub.ftlDescription}");
		 mv.addObject(NormalExcelConstants.CLASS, ${sub.entityName}.class);
		 mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("${sub.ftlDescription}报表", "导出人:" + sysUser.getRealname(), "${sub.ftlDescription}"));
		 mv.addObject(NormalExcelConstants.DATA_LIST, exportList);
		 return mv;
    }

    /**
     * 导入
     * @return
     */
    @RequestMapping(value = "/import${sub.entityName}/{mainId}")
    public Result<?> import${sub.entityName}(HttpServletRequest request, HttpServletResponse response, @PathVariable("mainId") String mainId) {
		 MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
		 Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
		 for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
       // 获取上传文件对象
			 MultipartFile file = entity.getValue();
			 ImportParams params = new ImportParams();
			 params.setTitleRows(2);
			 params.setHeadRows(1);
			 params.setNeedSave(true);
			 try {
				 List<${sub.entityName}> list = ExcelImportUtil.importExcel(file.getInputStream(), ${sub.entityName}.class, params);
				 for (${sub.entityName} temp : list) {
					<#list sub.foreignKeys as key>
                    temp.set${key?cap_first}(mainId);
                    </#list>
				 }
				 long start = System.currentTimeMillis();
				 ${sub.entityName?uncap_first}Service.saveBatch(list);
				 log.info("消耗时间" + (System.currentTimeMillis() - start) + "毫秒");
				 return Result.OK("文件导入成功！数据行数：" + list.size());
			 } catch (Exception e) {
				 log.error(e.getMessage(), e);
				 return Result.error("文件导入失败:" + e.getMessage());
			 } finally {
				 try {
					 file.getInputStream().close();
				 } catch (IOException e) {
					 e.printStackTrace();
				 }
			 }
		 }
		 return Result.error("文件导入失败！");
    }

    /*--------------------------------子表处理-${sub.ftlDescription}-end----------------------------------------------*/
	</#list>




}
